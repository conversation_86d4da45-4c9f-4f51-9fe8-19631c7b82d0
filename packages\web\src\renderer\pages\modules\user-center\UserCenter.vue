<template>
  <div class="user-center">
    <div class="user-center-header">
      <h2>{{ $t('个人中心') }}</h2>
    </div>
    
    <div class="user-center-content">
      <CacheManagement />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
// import UserInfo from './components/UserInfo.vue'
// import PersonalSettings from './components/PersonalSettings.vue'
// import PasswordChange from './components/PasswordChange.vue'
import CacheManagement from './components/CacheManagement.vue'

const activeTab = ref('userInfo')
onMounted(() => {
  const lastTab = localStorage.getItem('userCenter/activeTab')
  if (lastTab) {
    activeTab.value = lastTab
  }
})
watch(activeTab, (newTab) => {
  localStorage.setItem('userCenter/activeTab', newTab)
})
</script>

<style lang="scss" scoped>
.user-center {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  .user-center-content {
  }

}
</style>
