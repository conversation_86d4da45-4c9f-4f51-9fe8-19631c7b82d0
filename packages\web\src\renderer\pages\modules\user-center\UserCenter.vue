<template>
  <div class="user-center">
    <div class="user-center-header">
      <h2>{{ $t('个人中心') }}</h2>
    </div>
    
    <div class="user-center-content">
      <el-tabs v-model="activeTab" class="user-center-tabs">
        <!-- <el-tab-pane :label="$t('基本信息')" name="userInfo">
          <UserInfo />
        </el-tab-pane>
        
        <el-tab-pane :label="$t('个人设置')" name="settings">
          <PersonalSettings />
        </el-tab-pane> -->
        
        <el-tab-pane :label="$t('缓存管理')" name="cache">
          <CacheManagement />
        </el-tab-pane>
<!--         
        <el-tab-pane :label="$t('密码修改')" name="password">
          <PasswordChange />
        </el-tab-pane> -->
      </el-tabs>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
// import UserInfo from './components/UserInfo.vue'
// import PersonalSettings from './components/PersonalSettings.vue'
// import PasswordChange from './components/PasswordChange.vue'
import CacheManagement from './components/CacheManagement.vue'

const activeTab = ref('userInfo')
onMounted(() => {
  const lastTab = localStorage.getItem('userCenter/activeTab')
  if (lastTab) {
    activeTab.value = lastTab
  }
})
watch(activeTab, (newTab) => {
  localStorage.setItem('userCenter/activeTab', newTab)
})
</script>

<style lang="scss" scoped>
.user-center {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  .user-center-content {
  }

}
</style>
