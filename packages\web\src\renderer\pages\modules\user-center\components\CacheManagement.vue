<template>
  <div class="cache-management">
    
 </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { CacheInfo } from '@src/types/apidoc/cache'

// const detailDialogVisible = ref(false)
// const selectedCategory = ref<CacheCategory | null>(null)
// const cacheDetails = ref<CacheItem[]>([])
// const indexedDBSizeLoading = ref(false)
const cacheInfo = ref<CacheInfo>({
  localStroageSize: 0,
  indexedDBSize: 0,
  localStorageDetails: [],
  indexedDBDetails: []
})

/*
|--------------------------------------------------------------------------
| 获取缓存数据大小
|--------------------------------------------------------------------------
*/
// 获取 localStorage缓存
const getLocalStorage = () => {
  let size = 0;
  for (let key in localStorage) {
    if (localStorage.hasOwnProperty(key)) {
      const value = localStorage.getItem(key) || ''
      const byteSize = new Blob([value]).size
      size += byteSize
    }
  }
  cacheInfo.value.localStroageSize = size;
}
//获取indexedDB缓存
const getIndexedDB = async () => {
 
}

/*
|--------------------------------------------------------------------------
| 
|--------------------------------------------------------------------------
|
*/

onMounted(() => {
  getLocalStorage()
  getIndexedDB()
})
</script>

<style lang="scss" scoped>


</style>
