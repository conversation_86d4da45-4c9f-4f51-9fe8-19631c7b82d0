<template>
  <div class="cache-management">
    <!-- 缓存概览 -->
    <el-card class="overview-card">
      <template #header>
        <div class="card-header">
          <el-icon>
            <DataBoard />
          </el-icon>
          <span>{{ $t('缓存概览') }}</span>
        </div>
      </template>

      <div class="cache-overview">
        <div class="overview-item">
          <div class="overview-value">{{ formatSize(totalCacheSize) }}</div>
          <div class="overview-label">{{ $t('总缓存大小') }}</div>
        </div>
        <div class="overview-item">
          <div class="overview-value">{{ cacheCategories.length }}</div>
          <div class="overview-label">{{ $t('缓存分类') }}</div>
        </div>
        <div class="overview-item">
          <div class="overview-value">{{ totalCacheItems }}</div>
          <div class="overview-label">{{ $t('缓存项目') }}</div>
        </div>
      </div>
    </el-card>

    <!-- 缓存分类管理 -->
    <el-card class="categories-card">
      <template #header>
        <div class="card-header">
          <el-icon>
            <FolderOpened />
          </el-icon>
          <span>{{ $t('缓存分类管理') }}</span>
          <div class="header-actions">
            <el-button size="small" type="danger" @click="clearAllCache">
              {{ $t('清空所有缓存') }}
            </el-button>
            <el-button size="small" @click="refreshCacheInfo">
              {{ $t('刷新') }}
            </el-button>
          </div>
        </div>
      </template>

      <div class="cache-categories">
        <div v-for="category in cacheCategories" :key="category.name" class="cache-category">
          <div class="category-info">
            <div class="category-header">
              <el-icon class="category-icon">
                <component :is="category.icon" />
              </el-icon>
              <div class="category-details">
                <div class="category-name">{{ category.displayName }}</div>
                <div class="category-description">{{ category.description }}</div>
              </div>
            </div>

            <div class="category-stats">
              <div class="stat-item">
                <span class="stat-label">{{ $t('大小') }}:</span>
                <span class="stat-value">{{ formatSize(category.size) }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">{{ $t('项目数') }}:</span>
                <span class="stat-value">{{ category.itemCount }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">{{ $t('最后更新') }}:</span>
                <span class="stat-value">{{ formatDate(category.lastUpdated) }}</span>
              </div>
            </div>
          </div>

          <div class="category-actions">
            <el-button size="small" @click="viewCacheDetails(category)">
              {{ $t('查看详情') }}
            </el-button>
            <el-button size="small" type="warning" @click="clearCategoryCache(category)">
              {{ $t('清空') }}
            </el-button>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 缓存详情对话框 -->
    <el-dialog v-model="detailDialogVisible" :title="`${selectedCategory?.displayName} - ${$t('缓存详情')}`" width="60%">
      <div class="cache-details">
        <el-table :data="cacheDetails" stripe>
          <el-table-column prop="key" :label="$t('缓存键')" min-width="200" />
          <el-table-column prop="size" :label="$t('大小')" width="100">
            <template #default="{ row }">
              {{ formatSize(row.size) }}
            </template>
          </el-table-column>
          <el-table-column prop="createdAt" :label="$t('创建时间')" width="150">
            <template #default="{ row }">
              {{ formatDate(row.createdAt) }}
            </template>
          </el-table-column>
          <el-table-column :label="$t('操作')" width="100">
            <template #default="{ row }">
              <el-button size="small" type="danger" @click="deleteCacheItem(row)">
                {{ $t('删除') }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { DataBoard, FolderOpened, Document, Setting, User } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { formatDate } from '@/helper'
import { apidocCache } from '@/cache/apidoc'
import { standaloneCache } from '@/cache/standalone'
import { CacheInfo } from '@src/types/apidoc/cache'

const detailDialogVisible = ref(false)
const selectedCategory = ref<CacheCategory | null>(null)
const cacheDetails = ref<CacheItem[]>([])
const indexedDBSizeLoading = ref(false)
const cacheInfo = ref<CacheInfo>({
  localStroageSize: 0,
  indexedDBSize: 0
})

/*
|--------------------------------------------------------------------------
| 获取缓存数据大小
|--------------------------------------------------------------------------
*/
// 获取 localStorage 大小
const getLocalStorageSize = () => {
  let size = 0;
  for (let key in localStorage) {
    if (localStorage.hasOwnProperty(key)) {
      const value = localStorage.getItem(key) || ''
      const byteSize = new Blob([value]).size
      size += byteSize
    }
  }
  cacheInfo.value.localStroageSize = size;
}
//获取indexedDB缓存
const getIndexedDBSize = async () => {
  try {
    indexedDBSizeLoading.value = true;
    // 获取所有数据库
    const databases = await window.indexedDB.databases();
    const allData: Record<string, Record<string, any[]>> = {};

    // 遍历每个数据库
    for (const dbInfo of databases) {
      const dbName = dbInfo.name as string;
      allData[dbName] = {};

      // 打开数据库
      const db = await new Promise((resolve, reject) => {
        const request = indexedDB.open(dbName);
        request.onsuccess = () => resolve(request.result);
        request.onerror = () => reject(request.error);
      });

      // 遍历每个对象存储
      for (const storeName of Array.from(db.objectStoreNames)) {
        const storeData = await new Promise((resolve, reject) => {
          const transaction = db.transaction(storeName, 'readonly');
          const store = transaction.objectStore(storeName);
          const request = store.getAll();

          request.onsuccess = () => resolve(request.result);
          request.onerror = () => reject(request.error);
        });

        allData[dbName][storeName] = storeData;
      }

      db.close();
    }

    console.log("所有IndexedDB数据:", allData);
    return allData;
  } catch (error) {
    console.error("获取IndexedDB数据时出错:", error);
    indexedDBSizeLoading.value = true;
    throw error;
  }
}



onMounted(() => {
  getLocalStorageSize()
  getIndexedDBSize()
})
</script>

<style lang="scss" scoped>
.cache-management {
  .overview-card {
    margin-bottom: 24px;

    .card-header {
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 600;
      font-size: 16px;
    }

    .cache-overview {
      display: flex;
      gap: 32px;

      .overview-item {
        text-align: center;

        .overview-value {
          font-size: 24px;
          font-weight: 600;
          color: var(--theme-color);
          margin-bottom: 4px;
        }

        .overview-label {
          color: var(--gray-600);
          font-size: 14px;
        }
      }
    }
  }

  .categories-card {
    .card-header {
      display: flex;
      align-items: center;
      justify-content: space-between;

      >div:first-child {
        display: flex;
        align-items: center;
        gap: 8px;
        font-weight: 600;
        font-size: 16px;
      }

      .header-actions {
        display: flex;
        gap: 8px;
      }
    }
  }

  .cache-categories {
    .cache-category {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16px;
      border: 1px solid var(--gray-200);
      border-radius: var(--border-radius-base);
      margin-bottom: 12px;

      &:last-child {
        margin-bottom: 0;
      }

      .category-info {
        flex: 1;

        .category-header {
          display: flex;
          align-items: center;
          gap: 12px;
          margin-bottom: 8px;

          .category-icon {
            font-size: 20px;
            color: var(--theme-color);
          }

          .category-details {
            .category-name {
              font-weight: 600;
              margin-bottom: 2px;
            }

            .category-description {
              font-size: 12px;
              color: var(--gray-600);
            }
          }
        }

        .category-stats {
          display: flex;
          gap: 24px;
          font-size: 12px;

          .stat-item {
            .stat-label {
              color: var(--gray-600);
            }

            .stat-value {
              font-weight: 500;
              margin-left: 4px;
            }
          }
        }
      }

      .category-actions {
        display: flex;
        gap: 8px;
      }
    }
  }

  .cache-details {
    max-height: 400px;
    overflow-y: auto;
  }
}

@media (max-width: 768px) {
  .cache-overview {
    flex-direction: column;
    gap: 16px !important;
  }

  .cache-category {
    flex-direction: column;
    align-items: flex-start !important;
    gap: 12px;

    .category-actions {
      align-self: stretch;
      justify-content: flex-end;
    }
  }
}
</style>
